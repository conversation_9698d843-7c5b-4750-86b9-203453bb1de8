<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>تسجيل الدخول - شبكة الإنترنت</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      transition: all 0.3s ease;
      direction: rtl;
      text-align: right;
    }

    body.dark {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: #ecf0f1;
    }

    .container {
      max-width: 480px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      overflow: hidden;
      transition: all 0.3s ease;
    }

    body.dark .container {
      background: rgba(44, 62, 80, 0.95);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 30px 20px;
      text-align: center;
      color: white;
      position: relative;
    }

    body.dark .header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    }

    .header h2 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header .subtitle {
      margin-top: 8px;
      font-size: 14px;
      opacity: 0.9;
    }

    .content {
      padding: 30px;
    }

    .dark-toggle {
      position: absolute;
      top: 15px;
      right: 15px;
      padding: 8px 12px;
      border: none;
      border-radius: 20px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .dark-toggle:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    .login-form {
      margin-bottom: 30px;
    }

    .input-group {
      position: relative;
      margin-bottom: 20px;
    }

    input {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid #e1e8ed;
      border-radius: 12px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    input:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    body.dark input {
      background: #34495e;
      border-color: #4a5f7a;
      color: #ecf0f1;
    }

    body.dark input:focus {
      border-color: #667eea;
      background: #2c3e50;
    }

    .login-btn {
      width: 100%;
      padding: 15px;
      border: none;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .login-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .login-btn:active {
      transform: translateY(0);
    }

    .msg {
      margin: 15px 0;
      padding: 12px;
      background: #fee;
      color: #c53030;
      border-radius: 8px;
      border-left: 4px solid #e53e3e;
      font-size: 14px;
      display: none;
    }

    .msg:not(:empty) {
      display: block;
    }

    .section {
      margin-top: 30px;
      padding-top: 25px;
      border-top: 1px solid #e1e8ed;
    }

    body.dark .section {
      border-top-color: #4a5f7a;
    }

    .section h3 {
      margin: 0 0 20px 0;
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      color: #2d3748;
    }

    body.dark .section h3 {
      color: #e2e8f0;
    }

    .package-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .package-card {
      background: #f8f9fa;
      padding: 20px 15px;
      border-radius: 12px;
      text-align: center;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .package-card:hover {
      transform: translateY(-5px);
      border-color: #667eea;
      box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
    }

    body.dark .package-card {
      background: #34495e;
    }

    body.dark .package-card:hover {
      border-color: #667eea;
    }

    .package-name {
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    body.dark .package-name {
      color: #e2e8f0;
    }

    .package-price {
      font-size: 18px;
      font-weight: 700;
      color: #667eea;
    }

    .pos-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .pos-item {
      padding: 12px 15px;
      margin-bottom: 8px;
      background: #f8f9fa;
      border-radius: 8px;
      border-right: 4px solid #667eea;
      transition: all 0.3s ease;
    }

    .pos-item:hover {
      background: #e2e8f0;
      transform: translateX(-5px);
    }

    body.dark .pos-item {
      background: #34495e;
    }

    body.dark .pos-item:hover {
      background: #2c3e50;
    }
  </style>
  <script src="/md5.js"></script>
  <script>
    function doLogin() {
      document.sendin.username.value = document.login.username.value;
      document.sendin.password.value = hexMD5('$(chap-id)' + document.login.password.value + '$(chap-challenge)');
      document.sendin.submit();
      return false;
    }
    function toggleDark() {
      document.body.classList.toggle("dark");
    }
  </script>
</head>
<body>
  <div class="container">
    <div class="header">
      <button class="dark-toggle" onclick="toggleDark()">🌙</button>
      <h2>🌐 شبكة الإنترنت</h2>
      <div class="subtitle">مرحباً بك، يرجى تسجيل الدخول</div>
    </div>

    <div class="content">
      <div class="login-form">
        <!-- عرض رسائل الخطأ -->
        <div class="msg">$(error)</div>

        <!-- نموذج الدخول -->
        <form name="login" action="$(link-login-only)" method="post" onsubmit="return doLogin()">
          <div class="input-group">
            <input name="username" type="text" placeholder="اسم المستخدم" required>
          </div>
          <div class="input-group">
            <input name="password" type="password" placeholder="كلمة المرور" required>
          </div>
          <button type="submit" class="login-btn">تسجيل الدخول</button>
        </form>

        <!-- فورم خفي لإرسال بيانات مشفرة -->
        <form name="sendin" action="$(link-login-only)" method="post" style="display:none">
          <input type="hidden" name="username">
          <input type="hidden" name="password">
          <input type="hidden" name="dst" value="$(link-orig)">
          <input type="hidden" name="popup" value="true">
        </form>
      </div>

      <!-- الباقات -->
      <div class="section">
        <h3>💼 أسعار الباقات</h3>
        <div class="package-grid">
          <div class="package-card">
            <div class="package-name">الباقة الأساسية</div>
            <div class="package-price">$10 / شهر</div>
          </div>
          <div class="package-card">
            <div class="package-name">الباقة المتوسطة</div>
            <div class="package-price">$20 / شهر</div>
          </div>
          <div class="package-card">
            <div class="package-name">الباقة المميزة</div>
            <div class="package-price">$30 / شهر</div>
          </div>
        </div>
      </div>

      <!-- نقاط البيع -->
      <div class="section">
        <h3>📍 نقاط البيع</h3>
        <ul class="pos-list">
          <li class="pos-item">🏪 المتجر الرئيسي - وسط المدينة</li>
          <li class="pos-item">🏬 فرع 1 - شارع السوق</li>
          <li class="pos-item">🏢 فرع 2 - الحي الغربي</li>
        </ul>
      </div>
    </div>
  </div>
</body>
</html>
