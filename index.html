<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>تسجيل الدخول - شبكة الإنترنت</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      padding: 0;
      background: #f0f0f0;
      color: #333;
      text-align: center;
      transition: background 0.3s, color 0.3s;
    }
    body.dark {
      background: #121212;
      color: #f4f4f4;
    }
    .container {
      max-width: 450px;
      margin: 30px auto;
      padding: 20px;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 3px 8px rgba(0,0,0,0.1);
      transition: background 0.3s;
    }
    body.dark .container {
      background: #1e1e1e;
    }
    h2 {
      margin-bottom: 20px;
    }
    input {
      width: 100%;
      padding: 10px;
      margin: 8px 0;
      border: 1px solid #ccc;
      border-radius: 5px;
      outline: none;
    }
    button {
      width: 100%;
      padding: 10px;
      border: none;
      background: #007BFF;
      color: #fff;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
      transition: background 0.3s;
    }
    button:hover {
      background: #0056b3;
    }
    .msg {
      margin: 10px 0;
      color: red;
      font-size: 14px;
    }
    .packages, .pos {
      margin-top: 25px;
      text-align: right;
    }
    .packages h3, .pos h3 {
      margin-bottom: 10px;
      text-align: center;
    }
    .package-list {
      display: flex;
      justify-content: space-between;
      gap: 10px;
      flex-wrap: wrap;
    }
    .package {
      flex: 1;
      min-width: 120px;
      background: #f9f9f9;
      padding: 10px;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      transition: background 0.3s, transform 0.3s;
    }
    .package:hover {
      transform: translateY(-3px);
    }
    body.dark .package {
      background: #2a2a2a;
    }
    .pos ul {
      list-style: none;
      padding: 0;
    }
    .pos li {
      padding: 5px 0;
    }
    .dark-toggle {
      margin: 15px 0;
      padding: 8px 16px;
      border: none;
      border-radius: 5px;
      background: #444;
      color: #fff;
      cursor: pointer;
    }
  </style>
  <script src="/md5.js"></script>
  <script>
    function doLogin() {
      document.sendin.username.value = document.login.username.value;
      document.sendin.password.value = hexMD5('$(chap-id)' + document.login.password.value + '$(chap-challenge)');
      document.sendin.submit();
      return false;
    }
    function toggleDark() {
      document.body.classList.toggle("dark");
    }
  </script>
</head>
<body>

  <div class="container">
    <h2>🌐 شبكة الإنترنت</h2>

    <!-- زر الوضع الليلي -->
    <button class="dark-toggle" onclick="toggleDark()">تبديل الوضع الليلي</button>

    <!-- عرض رسائل الخطأ -->
    <div class="msg">$(error)</div>

    <!-- نموذج الدخول -->
    <form name="login" action="$(link-login-only)" method="post" onsubmit="return doLogin()">
      <input name="username" type="text" placeholder="اسم المستخدم" required>
      <input name="password" type="password" placeholder="كلمة المرور" required>
      <button type="submit">تسجيل الدخول</button>
    </form>

    <!-- فورم خفي لإرسال بيانات مشفرة -->
    <form name="sendin" action="$(link-login-only)" method="post" style="display:none">
      <input type="hidden" name="username">
      <input type="hidden" name="password">
      <input type="hidden" name="dst" value="$(link-orig)">
      <input type="hidden" name="popup" value="true">
    </form>

    <!-- الباقات -->
    <div class="packages">
      <h3>💼 أسعار الباقات</h3>
      <div class="package-list">
        <div class="package">
          <strong>الباقة الأساسية</strong><br>
          10$ / شهر
        </div>
        <div class="package">
          <strong>الباقة المتوسطة</strong><br>
          20$ / شهر
        </div>
        <div class="package">
          <strong>الباقة المميزة</strong><br>
          30$ / شهر
        </div>
      </div>
    </div>

    <!-- نقاط البيع -->
    <div class="pos">
      <h3>📍 نقاط البيع</h3>
      <ul>
        <li>المتجر الرئيسي - وسط المدينة</li>
        <li>فرع 1 - شارع السوق</li>
        <li>فرع 2 - الحي الغربي</li>
      </ul>
    </div>
  </div>

</body>
</html>
